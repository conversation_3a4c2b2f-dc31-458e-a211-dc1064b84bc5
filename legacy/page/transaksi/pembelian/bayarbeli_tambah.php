<?php

use App\Models\BeliBayar;
use App\Models\JurnalDetail;
use Illuminate\Support\Facades\DB;

if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $data = DB::table('tb_beli')
        ->join('tb_supl', 'tb_beli.idsupl', '=', 'tb_supl.supl_kd')
        ->where('tb_beli.nobeli', $id)
        ->first();

    if ($data) {
        $xnobeli = $data->nobeli;
        $xtglbeli = $data->tglbeli;
        $xtgljt = $data->tgljt;
        $xjppn = $data->jppn;
        $xsupl = $data->idsupl;
        $xnmsupl = $data->nama_pt;
        $xketmaster = $data->ketbeli;
    }
}
?>
<section class="content">
    <div class="box box-info box-solid">
        <div class="box-header with-border">
            INPUT PEMBAYARAN
        </div>
        <div class="panel-body">
            <div class="col-md-4">
                <div class="table-responsive">

                    <?php
                    $nobeli = $xnobeli;
$tgl = $xtglbeli;
$tgljt = $xtgljt;
$ketmaster = $xketmaster;
?>
                    <form method="post">
                        <table width='100%'>
                            <td width='30%'>Transaksi</td>
                            <td width='5%'>:</td>
                            <td>
                                <input STYLE="color: #000; background-color: #f7f7f0;" type="text" name="nobeli" class="form-control" required="" value="<?php echo $nobeli ?>" readonly></td>
                            </tr>

                            <tr>
                                <td width='30%'>Tanggal</td>
                                <td width='5%'>:</td>
                                <td>
                                    <input required="" type="date" name="tglbeli" class="form-control" value="<?php echo $tgl ?>" readonly></td>
                            </tr>
                            <tr>
                                <td width='30%'>Jatuh Tempo</td>
                                <td width='5%'>:</td>
                                <td>
                                    <input required="" type="date" name="tgljt" class="form-control" value="<?php echo $tgljt ?>" readonly></td>
                            </tr>
                            <tr>
                                <td width='30%'>Supplier</td>
                                <td width='5%'>:</td>
                                <td>
                                    <select name="supl" id="supl" class="form-control" required="required" readonly>
                                        <option value=""></option>
                                        <?php
                $suppliers = DB::table('tb_supl')->orderBy('nama_pt')->get();
foreach ($suppliers as $supplier) {
    $selected = selected($supplier->supl_kd, $xsupl, false);
    echo "<option value=\"{$supplier->supl_kd}\" {$selected}>{$supplier->nama_pt}</option>";
}
?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td width='30%'>Keterangan</td>
                                <td width='5%'>:</td>
                                <td>
                                    <input required="" type="text" name="ketmaster" class="form-control" value="<?php echo $ketmaster ?>" readonly></td>
                            </tr>
                        </table>
                        <hr>

                        <table width='100%'>
                            <tr>
                                <td colspan="3" align="left"> &nbsp;&nbsp;Detail Pembayaran :</td>
                            </tr>
                            <tr>
                                <?php
$nobayar = BeliBayar::getNextNumber();

$purchase = DB::table('tb_beli')->where('nobeli', $xnobeli)->first();
$hut = $purchase->totbayar;

$totalPayments = DB::table('tb_belibayar')
    ->where('ponomor', $xnobeli)
    ->sum('jmlbayar');

$zbyr = $totalPayments;
$kurang = $hut - $zbyr;
?>

                                <td width='30%'>&nbsp;&nbsp;No. Bayar</td>
                                <td width='5%'>:</td>
                                <td>
                                    <input required="" type="text" value="<?php echo $nobayar; ?>" name="nobyr" id="nobyr" class="form-control">
                                </td>
                            </tr>
                            <tr>
                                <td width='30%'>&nbsp;&nbsp;Tanggal</td>
                                <td width='5%'>:</td>
                                <td>
                                    <input required="" type="date" name="tglbyr" class="form-control" value="<?php echo date('Y-m-d'); ?>"></td>
                            </tr>
                            <tr>
                                <td width='30%'>&nbsp;&nbsp;Rekening</td>
                                <td width='5%'>:</td>
                                <td>
                                    <select required="" class="form-control" name="rekbyr" id="rekbyr">
                                        <option value="">-Pilih-</option>
                                        <?php
    $accounts = DB::table('tb_rekening')
        ->whereRaw('LEFT(norek, 3) IN ("111", "112")')
        ->orderBy('nmrek')
        ->get();

foreach ($accounts as $account) {
    echo "<option value='{$account->norek}'>{$account->nmrek}</option>";
}
?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td width='30%'>&nbsp;&nbsp;Jumlah</td>
                                <td width='5%'>:</td>
                                <td>
                                    <input required="" type="number" onchange="setTwoNumberDecimal" step="any" value="<?php echo $kurang ?>" name="jmlbyr" id="jmlbyr" class="form-control">
                                </td>
                            </tr>
                            <tr>
                                <td width='30%'>&nbsp;&nbsp;Arus Kas</td>
                                <td width='5%'>:</td>
                                <td>
                                    <select name="cashflow" class="form-control" required>
                                        <option value="">- Pilih -</option>
                                        <?php
                                        foreach (JurnalDetail::CASHFLOWS as $flow) {
                                            echo "<option value=\"{$flow}\">{$flow}</option>";
                                        }
?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align='right'>
                                    <hr>
                                    <a href="?page=byrhutang" class="btn btn-sm btn-danger">Exit</a>
                                    <button type="reset" name="reset" class="btn btn-sm btn-warning">Clear</button>
                                    <button type="submit" name="tambah" class="btn btn-sm btn-primary">Simpan</button>
                                </td>
                            </tr>
                        </table>
                    </form>

                </div>
            </div>

            <div class="col-md-8" valign='top'>
                <div class="table-responsive">
                    Rincian Detail :
                    <table class="table table-bordered table-hover table-striped">
                        <thead>
                            <tr bgcolor='#3a9ad1'>
                                <th>
                                    <font color="#FFF">
                                        <center>No</center>
                                    </font>
                                </th>
                                <th>
                                    <font color="#FFF">
                                        <center>Nama Barang</center>
                                    </font>
                                </th>
                                <th>
                                    <font color="#FFF">
                                        <center>Harga</center>
                                    </font>
                                </th>
                                <th>
                                    <font color="#FFF">
                                        <center>Qty</center>
                                    </font>
                                </th>
                                <th>
                                    <font color="#FFF">
                                        <center>Jumlah</center>
                                    </font>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
$totkasmsk = 0;

$details = DB::table('tb_belidtl')
    ->join('tb_barang', 'tb_belidtl.kdbrg', '=', 'tb_barang.barang_kd')
    ->where('nobelidtl', $id)
    ->orderBy('id_belidtl')
    ->get();

foreach ($details as $d) {
    $totkasmsk += $d->sharga;
    ?>
                                <tr>
                                    <td width="1%" align="center"><?php echo $no++; ?></td>
                                    <td><?php echo $d->nama_barang ?></td>
                                    <td width="15%" align="right"><?php echo number_format($d->harga, 2) ?></td>
                                    <td width="15%" align="right"><?php echo number_format($d->jmlbrg, 2) ?></td>
                                    <td width="15%" align="right"><?php echo number_format($d->sharga, 2) ?></td>
                                </tr>

                                <!-- Edit Detail-->
                                <div class="modal fade" id="mymodal<?php echo $d->id_belidtl; ?>">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="box box-primary box-solid">
                                                <div class="box-header with-border">
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span></button>
                                                    Edit Barang
                                                </div>
                                                <div class="modal-body">
                                                    <form role="form" method="POST">
                                                        <?php
                                $belidtl_id = $d->id_belidtl;
    $detail = DB::table('tb_belidtl')
        ->join('tb_barang', 'tb_belidtl.kdbrg', '=', 'tb_barang.barang_kd')
        ->where('id_belidtl', $belidtl_id)
        ->first();
    ?>

                                                        <input type="hidden" name="belidtl_id" value="<?php echo $detail->id_belidtl; ?>">
                                                        <input type="hidden" name="nobelidtl" value="<?php echo $detail->nobelidtl; ?>">

                                                        <div class="form-group">
                                                            <label>Barang</label>
                                                            <input type="text" class="form-control" name="xnmbrg" value="<?php echo $d->nama_barang; ?>" readonly>
                                                        </div>
                                                        <div class="form-group">
                                                            <label>Satuan</label>
                                                            <input type="text" class="form-control" name="xsatuan" value="<?php echo $d->satuan; ?>" readonly>
                                                        </div>
                                                        <div class="form-group">
                                                            <label>Harga</label>
                                                            <input type="number" class="form-control" name="xharga" value="<?php echo $d->harga; ?>" required="required">
                                                        </div>
                                                        <div class="form-group">
                                                            <label>Jumlah</label>
                                                            <input type="number" class="form-control" name="xjml" value="<?php echo $d->jmlbrg; ?>" required="required">
                                                        </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="submit" name="simpanedit" class="btn btn-primary">Simpan</button>
                                                </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
}
?>
                                    <tr>
                                        <td align="left" colspan="4"><b>Jumlah </b></td>
                                        <td align="right"><b><?php echo number_format($totkasmsk, 2); ?></b></td>
                                    </tr>
                                    <?php
if (isset($_POST['simpanedit'])) {
    $belidtl_id = $_POST['belidtl_id'];
    $xharga = $_POST['xharga'];
    $xjml = $_POST['xjml'];
    $nobelidtl = $_POST['nobelidtl'];
    $xsharga = $xjml * $xharga;

    DB::table('tb_belidtl')
        ->where('id_belidtl', $belidtl_id)
        ->update([
            'harga' => $xharga,
            'jmlbrg' => $xjml,
            'sharga' => $xsharga,
        ]);

    $totbelidtl = DB::table('tb_belidtl')
        ->where('nobelidtl', $nobelidtl)
        ->sum('sharga');

    DB::table('tb_beli')
        ->where('nobeli', $nobelidtl)
        ->update(['jumlah' => $totbelidtl]);

    echo "
                                        <script>
                                            setTimeout(function() {
                                                swal({
                                                    title: 'Transaksi Pembelian',
                                                    text: 'Berhasil Disimpan!',
                                                    type: 'success'
                                                }, function() {
                                                    window.location ='?page=beli&aksi=tambah&id={$nobelidtl}';
                                                });
                                            }, 300);
                                        </script>
                                        ";
}
?>


                        </tbody>
                    </table>

                    <div class="col-md-6">
                    </div>
                    <div class="col-md-6">
                        <form method="post">
                            <input type="hidden" name="nobeli" value="<?php echo $id; ?>">

                            <table width='100%'>
                                <?php
                                $purchase = DB::table('tb_beli')->where('nobeli', $id)->first();
if ($purchase) {
    $zjumlah = $purchase->jumlah;
    $zangkut = $purchase->angkut;
    $zppn = $purchase->ppn;
    $ztotal = $purchase->totbayar;
    $zdibayar = $purchase->dibayar;
    $zjppn = $purchase->jppn;
}
?>
                                <tr>
                                    <td width='30%'>Jumlah</td>
                                    <td width='5%'>:</td>
                                    <td>
                                        <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jumlah" id="jumlah" class="form-control" value="<?php echo number_format($zjumlah, 0, ',', '.'); ?>" readonly></td>
                                    </td>
                                </tr>
                                <tr>
                                    <td width='30%'>Ppn &nbsp;&nbsp;
                                        <?php
    if ($zjppn == 'Ppn') {
        ?>
                                            <input type="hidden" name="cekppn" value="Non Ppn" disabled>
                                            <input type="checkbox" name="cekppn" id="cekppn" value="Ppn" checked disabled>

                                        <?php
    } else {
        ?>
                                            <input type="hidden" name="cekppn" value="Non Ppn" disabled>
                                            <input type="checkbox" name="cekppn" id="cekppn" value="Ppn" disabled>
                                        <?php
    }
?>
                                    </td>
                                    <td width='5%'>:</td>
                                    <td>

                                        <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jmlppn" id="jmlppn" class="form-control" value="<?php echo number_format($zppn, 0, ',', '.'); ?>" readonly></td>
                                    </td>
                                </tr>
                                <tr>
                                    <td width='30%'>By Angkut</td>
                                    <td width='5%'>:</td>
                                    <td>
                                        <?php
if ($id != '') { ?>
                                            <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jmlangkut" id="jmlangkut" class="form-control" value="<?php echo number_format($zangkut, 0, ',', '.'); ?>" readonly></td>
                                    <?php
} else { ?>
                                        <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jmlangkut" id="jmlangkut" class="form-control" value="" readonly></td>
                                    <?php
} ?>

                                    </td>
                                </tr>
                                <tr>
                                    <td width='30%'>Total</td>
                                    <td width='5%'>:</td>
                                    <td>
                                        <?php
if ($id != '') { ?>
                                            <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jmltotal" id="jmltotal" class="form-control" value="<?php echo number_format($ztotal, 0, ',', '.'); ?>" readonly></td>
                                    <?php
} else { ?>
                                        <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jmltotal" id="jmltotal" class="form-control" value="" readonly></td>
                                    <?php
} ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width='30%'>Dibayar</td>
                                    <td width='5%'>:</td>
                                    <td>

                                        <?php
if ($id != '') { ?>
                                            <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jmldibayar" id="jmldibayar" class="form-control" value="<?php echo number_format($zdibayar + $zbyr, 0, ',', '.')  ?>" readonly></td>
                                        <?php
} else { ?>
                                            <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jmldibayar" id="jmldibayar" class="form-control" value=""></td>
                                        <?php
} ?>
                                    </td>
                                </tr>

                                <tr>
                                    <td width='30%'>Kurang Bayar</td>
                                    <td width='5%'>:</td>
                                    <td>

                                        <input style="text-align:right;" type="text" onchange="setTwoNumberDecimal" step="any" name="jmlkurang" id="jmlkurang" class="form-control" value="<?php echo number_format($kurang, 0, ',', '.') ?>" readonly></td>

                                    </td>
                                </tr>


                            </table>
                    </div>
                    </form>


                </div>
            </div>




            <?php
            if (isset($_POST['tambah'])) {
                $nobyr = $_POST['nobyr'];
                $nobeli = $_POST['nobeli'];
                $supl = $_POST['supl'];
                $tglbyr = $_POST['tglbyr'];
                $rekbyr = $_POST['rekbyr'];
                $jmlbyr = $_POST['jmlbyr'];
                $cashflow = $_POST['cashflow'];

                $beliBayar = BeliBayar::query()->create([
                    'nobayar' => $nobyr,
                    'tglbayar' => $tglbyr,
                    'ponomor' => $nobeli,
                    'posupl' => $supl,
                    'rekbayar' => $rekbyr,
                    'jmlbayar' => $jmlbyr,
                    'cashflow' => $cashflow,
                ]);
                $beliBayar->syncJournalEntry();

                echo "
                <script>
                    setTimeout(function() {
                        swal({
                            title: 'Transaksi Pembayaran',
                            text: 'Berhasil Disimpan!',
                            type: 'success'
                        }, function() {
                            window.location ='?page=byrhutang';
                        });
                    }, 300);
                </script>
                ";
            }
?>
            <script src="dist/js/jquery.min.js"></script>
            <script type="text/javascript">
                function changeBarang(brg) {
                    var xbrg = document.getElementById('brg').value;
                    if (xbrg == "") {
                        document.getElementById('hrg').value = 0;
                        document.getElementById('sat').value = "";
                    } else {
                        document.getElementById('hrg').value = dtArray3[brg].hpp;
                        document.getElementById('sat').value = dtArray3[brg].satuan;
                        document.getElementById('jml').focus();
                    }
                };

                $(document).ready(function() {
                    $("#jmlangkut").keyup(function() {
                        var xjml = $("#jumlah").val();
                        var xppn = $("#jmlppn").val();
                        var xangkut = $("#jmlangkut").val();
                        var tot = Number(xjml) + Number(xppn) + Number(xangkut)
                        $("#jmltotal").val(tot);
                    });

                    $("#cekppn").change(function() {
                        if (document.getElementById("cekppn").checked) {
                            var xjml = $("#jumlah").val();
                            var xjmlppn = xjml * 0.1;
                            $("#jmlppn").val(xjmlppn);
                        } else {
                            $("#jmlppn").val(0);
                        }

                        var yjml = $("#jumlah").val();
                        var xppn = $("#jmlppn").val();
                        var xangkut = $("#jmlangkut").val();
                        var tot = Number(yjml) + Number(xppn) + Number(xangkut)
                        $("#jmltotal").val(tot);
                    });

                });
            </script>
        </div>
    </div>
</section>
