<?php

use App\Models\Beli;
use Illuminate\Support\Facades\DB;

$awal = $_GET['from'] ?? date('Y-m-d', strtotime('first day of this month'));
$akhir = $_GET['to'] ?? date('Y-m-d', strtotime('last day of this month'));
?>
<?php legacy_view('layouts.content-header', [
    'title' => 'Daftar Pembelian',
    'breadcrumbs' => [
        'Transaksi' => '#',
        'Pembelian' => '#',
        'Pembayaran PO' => '/?page=byrhutang',
        'Daftar Pembelian' => null,
    ],
]); ?>
<section class="content">
    <div class="box">
        <div class="box-body">
            <form action="" method="get">
                <input type="hidden" name="page" value="byrhutang">
                <input type="hidden" name="aksi" value="listbayar">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Awal</label>
                            <input name="from" class="form-control" value="<?php echo $awal; ?>" type="date">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Akhir</label>
                            <input name="to" class="form-control" value="<?php echo $akhir; ?>" type="date">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label><br></label>
                            <div>
                                <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> Preview</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="box">
        <div class="box-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover" id="tabel-pembelian">

            <thead>
                <tr style="background-color: #4095d2; color: #fff; text-align: center;">
                    <th width="1%">NO</th>
                    <th width="10%">NO.BUKTI</th>
                    <th width="10%">JT TEMPO</th>
                    <th>SUPPLIER</th>
                    <th>KETERANGAN</th>
                    <th width="10%">JUMLAH</th>
                    <th width="10%">RETUR</th>
                    <th width="10%">DIBAYAR</th>
                    <th width="10%">HUTANG</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
				<?php
$no = 1;
$data = Beli::query()
    ->join('tb_supl', 'tb_beli.idsupl', '=', 'tb_supl.supl_kd')
    ->leftJoin('tb_belibayar', 'tb_beli.nobeli', '=', 'tb_belibayar.ponomor')
    ->leftJoin('tb_beliretur', 'tb_beli.nobeli', '=', 'tb_beliretur.returnobeli')
    ->whereDateBetween('tglbeli', [$awal, $akhir])
    ->groupBy([
        'tb_beli.id_beli',
        'tb_beli.nobeli',
        'tb_beli.tglbeli',
        'tb_beli.tgljt',
        'tb_beli.dibayar',
        'tb_beli.totbayar',
        'tb_beli.ketbeli',
        'tb_supl.nama_pt',
    ])
    ->select([
        'tb_beli.id_beli',
        'tb_beli.nobeli',
        'tb_beli.tglbeli',
        'tb_beli.tgljt',
        'tb_beli.dibayar',
        'tb_beli.totbayar',
        'tb_beli.ketbeli',
        'tb_supl.nama_pt',
        DB::raw('COALESCE(SUM(tb_belibayar.jmlbayar), 0) as total_bayar'),
        DB::raw('COALESCE(SUM(tb_beliretur.jumlahretur), 0) as total_retur'),
    ])
    ->orderByDesc('nobeli')
    ->get();

foreach ($data as $d) {
    $zbyr = $d->dibayar + $d->total_bayar;
    $zsaldo = $d->totbayar - $zbyr - $d->total_retur;
    ?>
                <tr>
                <td align="center"><?php echo $no++; ?></td>
				<td align="left"><?php echo $d->nobeli ?><br><?php echo $d->tglbeli ?></td>
				<td align="center"><b><?php echo $d->tgljt ?></b></td>
				<td align="left"><?php echo $d->nama_pt ?></td>
				<td align="left"><?php echo $d->ketbeli ?></td>
				<td align="right"><?php echo number_format($d->totbayar, 0, ',', '.') ?></td>
                <td align="right"><?php echo number_format($d->total_retur, 0, ',', '.') ?></td>
				<td align="right"><?php echo number_format($zbyr, 0, ',', '.') ?></td>
				<td align="right"><?php echo number_format($zsaldo, 0, ',', '.') ?></td>

                <td align='center'>
				<?php if ($zsaldo <= 0) { ?>
                    <?php echo 'Lunas' ?>
				<?php } else { ?>
                    <a href="?page=byrhutang&aksi=tambah&id=<?php echo $d->nobeli; ?>" class="btn btn-info btn-sm">Bayar</a>
				<?php } ?>
                </td>
                </tr>
                <?php } ?>
            </tbody>
            <tfoot>
                <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th class="text-right"></th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
        </div>
</section>

<script>
  $(function () {
		$('#tabel-pembelian').DataTable({
			footerCallback: function (row, data, start, end, display) {
				var api = this.api(), data;
				// Remove the formatting to get integer data for summation
				var intVal = function (i) {
					return typeof i === 'string'
						? i.replace(/[\$.]/g, '') * 1
						: typeof i === 'number'
							? i : 0;
				};

				var total = api
					.cells(null, 8, { search: 'applied'})
					.render('display')
					.reduce(function (a, b) {
						return intVal(a) + intVal(b);
					}, 0);

				$(api.column(8).footer()).html(formatRupiah(total, 0));
			},
		});
  });
</script>
