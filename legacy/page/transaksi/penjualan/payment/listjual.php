<?php
use App\Models\Customer;
use Illuminate\Support\Facades\DB;

$awal = $_GET['from'] ?? date('Y-m-d', strtotime('first day of this month'));
$akhir = $_GET['to'] ?? date('Y-m-d', strtotime('last day of this month'));
$cust = $_GET['cust'] ?? 'all';
?>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info box-solid">
                <div class="box-header with-border">
                    DAFTAR INVOICE
                </div>
                <div class="panel-body">
                    <a href="?page=byrpiutang">&#8610; Back</a><br><br>
                    <form action="" method="GET">
                        <input type="hidden" name="page" value="byrpiutang">
                        <input type="hidden" name="aksi" value="list">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Awal</label>
                                    <input name="from" class="form-control tanggal" value="<?php echo $awal; ?>" type="date">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Akhir</label>
                                    <input name="to" class="form-control tanggal" value="<?php echo $akhir; ?>" type="date">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Customer</label>
                                    <div class="input-group date">
                                        <select name="cust" id="cust" class="form-control select2" style="text-align: left;">
                                            <option value="all" <?php echo $cust == 'all' ? 'selected' : ''; ?>>-semua-</option>
                                            <?php
                                            $customers = Customer::orderBy('nama_cust')->get();
foreach ($customers as $customer) {
    echo '<option value="' . $customer->cust_kd . '" ' . ($cust == $customer->cust_kd ? 'selected' : '') . '>' . $customer->nama_cust . '</option>';
}
?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><br></label>
                                    <div>
                                        <button type="submit" style="margin-top: 0px;" class="btn btn-success" data-toggle="tooltip" title="Preview"><i class="fa fa-search"></i> Preview</button>
                                        <a target="_blank" href="/page/transaksi/penjualan/payment/listjual_excel.php?from=<?php echo $awal; ?>&to=<?php echo $akhir; ?>&cust=<?php echo $cust; ?>" class="btn btn-warning" id="excel" data-toggle="tooltip" title="Excel"><i class="fa fa-file-excel-o"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div style="margin-bottom: 10px;">
                        <button type="button" id="bulk-payment" class="btn btn-primary" disabled>
                            <i class="fa fa-money"></i> Bayar Invoice Terpilih
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover" id="tabel-invoice">
                            <thead>
                                <tr style="background-color: #4095d2; color: #fff">
                                    <th style="width: 1%" class="text-center">
                                        <input type="checkbox" id="check-all">
                                    </th>
                                    <th style="width: 1%" class="text-center">NO</th>
                                    <th style="width: 15%" class="text-center">NO. INVOICE / ORDER</th>
                                    <th style="width: 10%" class="text-center">TANGGAL</th>
                                    <th class="text-center">CUSTOMER</th>
                                    <th style="width: 10%" class="text-center">JUMLAH</th>
                                    <th style="width: 10%" class="text-center">ONGKIR</th>
                                    <th style="width: 10%" class="text-center">PACKING</th>
                                    <th style="width: 10%" class="text-center">PIUTANG</th>
                                    <th style="width: 10%" class="text-center">DIBAYAR</th>
                                    <th style="width: 10%" class="text-center">SALDO</th>
                                    <th style="width: 10%" class="text-center">OPSI</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
$query = DB::table('tb_jual as j')
    ->select('j.nojual', 'j.norder', 'j.jumlah', 'j.ongkir', 'j.packing', 'j.total', 'j.tgljual',
        DB::raw('COALESCE(SUM(jb.jmlbayar), 0) as totbayar'), 'c.nama_cust')
    ->leftJoin('tb_jualbayar as jb', 'j.nojual', '=', 'jb.nomor_inv')
    ->leftJoin('tb_cust as c', 'j.kdcust', '=', 'c.cust_kd')
    ->whereBetween(DB::raw('date(j.tgljual)'), [$awal, $akhir])
    ->groupBy('j.nojual', 'j.jumlah', 'j.ongkir', 'j.packing', 'j.total', 'j.tgljual', 'c.nama_cust')
    ->orderByDesc('j.nojual');

if ($cust !== 'all') {
    $query->where('j.kdcust', $cust);
}

$results = $query->get();

foreach ($results as $d) {
    $zsaldo = $d->total - $d->totbayar;

    if ($zsaldo > 0) {
        echo "<tr>
                                            <td align='center'>
                                                <input type='checkbox' class='invoice-check' value='{$d->nojual}' data-amount='{$zsaldo}'>
                                            </td>
                                            <td align='center'>{$no}</td>
                                            <td align='left'>
                                                <p>{$d->nojual}</p>
                                                <p>{$d->norder}</p>
                                            </td>
                                            <td align='left'>{$d->tgljual}</td>
                                            <td align='left'>{$d->nama_cust}</td>
                                            <td align='right'>" . number_format($d->jumlah, 0, ',', '.') . "</td>
                                            <td align='right'>" . number_format($d->ongkir, 0, ',', '.') . "</td>
                                            <td align='right'>" . number_format($d->packing, 0, ',', '.') . "</td>
                                            <td align='right'>" . number_format($d->total, 0, ',', '.') . "</td>
                                            <td align='right'>" . number_format($d->totbayar, 0, ',', '.') . "</td>
                                            <td align='right'>" . number_format($zsaldo, 0, ',', '.') . "</td>
                                            <td align='center'>
                                                <a href='?page=byrpiutang&aksi=tambah&id={$d->nojual}' class='btn btn-info btn-sm'>Bayar</a>
                                            </td>
                                        </tr>";
        $no++;
    }
}
?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th class="text-right"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function processSelectedInvoices() {
    var selectedInvoices = [];
    $('.invoice-check:checked').each(function() {
        selectedInvoices.push($(this).val());
    });

    if (selectedInvoices.length > 0) {
        var queryString = selectedInvoices.map(function(invoice) {
            return 'invoices[]=' + encodeURIComponent(invoice);
        }).join('&');

        window.location.href = '?page=byrpiutang&aksi=tambah&' + queryString;
    }
}

$(function () {
    // Handle check all functionality
    $('#check-all').change(function() {
        $('.invoice-check').prop('checked', $(this).prop('checked'));
        updateBulkPaymentButton();
    });

    // Handle individual checkbox changes
    $(document).on('change', '.invoice-check', function() {
        updateBulkPaymentButton();

        // Update header checkbox
        var allChecked = $('.invoice-check:not(:checked)').length === 0;
        $('#check-all').prop('checked', allChecked);
    });

    // Handle bulk payment button click
    $('#bulk-payment').click(function() {
        processSelectedInvoices();
    });

    function updateBulkPaymentButton() {
        var checkedCount = $('.invoice-check:checked').length;
        $('#bulk-payment').prop('disabled', checkedCount === 0);
    }

    $('#tabel-invoice').DataTable({
        order: [[1, 'asc']],
        columnDefs: [
            { orderable: false, targets: 0 }
        ],
        footerCallback: function (row, data, start, end, display) {
            var api = this.api(), data;
            // Remove the formatting to get integer data for summation
            var intVal = function (i) {
                return typeof i === 'string'
                    ? i.replace(/[\$.]/g, '') * 1
                    : typeof i === 'number'
                        ? i : 0;
            };

            var total = api
                .cells(null, 10, { search: 'applied' })
                .render('display')
                .reduce(function (a, b) {
                    return intVal(a) + intVal(b);
                }, 0);

            $(api.column(10).footer()).html(formatRupiah(total));
        },
    });
});
</script>
