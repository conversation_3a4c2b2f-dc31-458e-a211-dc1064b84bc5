<?php
use App\Models\Customer;
use App\Models\JualBayar;
use App\Models\JurnalDetail;
use App\Models\Rekening;
use Illuminate\Support\Facades\DB;

// Validate input
$invoices = $_GET['invoices'] ?? [$_GET['id'] ?? null];
$invoices = is_array($invoices) ? $invoices : [$invoices];

// Get sale data with customer info
$sale = DB::table('tb_jual')
    ->select('tb_jual.*', 'tb_cust.*', DB::raw('DATE_ADD(tb_jual.tgljual, INTERVAL termin DAY) as jt'))
    ->join('tb_cust', 'tb_jual.kdcust', '=', 'tb_cust.cust_kd')
    ->whereIn('tb_jual.nojual', $invoices)
    ->first();

if (! $sale) {
    header('Location: ?page=byrpiutang');
    exit;
}

// Calculate totals
$totals = DB::table('tb_jual')->whereIn('nojual', $invoices)->selectRaw('
    SUM(jumlah) as subtotal,
    SUM(ongkir) as shipping,
    SUM(packing) as packing,
    SUM(total) as total
')->first();

$paid = DB::table('tb_jualbayar')
    ->whereIn('nomor_inv', $invoices)
    ->sum('jmlbayar');

$remaining = $totals->total - $paid;

// Get sale details
$details = DB::table('tb_jualdtl')
    ->join('tb_barang', 'tb_jualdtl.kdproduk', '=', 'tb_barang.barang_kd')
    ->whereIn('nojualdtl', $invoices)
    ->orderBy('jualdtl_id')
    ->get();

// Handle form submission
if (isset($_POST['tambah'])) {
    // Validate customer
    $invoice_customers = DB::table('tb_jual')
        ->whereIn('nojual', explode(', ', $_POST['nojual']))
        ->pluck('kdcust')
        ->unique();

    if ($invoice_customers->count() > 1) {
        echo "<script>
            setTimeout(function() {
                swal({
                    title: 'Error',
                    text: 'Semua invoice harus dari customer yang sama!',
                    type: 'error'
                });
            }, 100);
        </script>";

        return;
    }

    // Parse payment amount
    $amount = str_replace(',', '.', str_replace('.', '', $_POST['jmlbyr']));

    if ($amount > $remaining) {
        echo "<script>
            setTimeout(function() {
                swal({
                    title: 'Error',
                    text: 'Jumlah pembayaran melebihi sisa piutang!',
                    type: 'error'
                });
            }, 100);
        </script>";

        return;
    }

    // Generate payment number
    $paymentNumber = JualBayar::getNextNumber();

    // Begin transaction
    DB::beginTransaction();
    try {
        $invoiceList = collect(explode(', ', $_POST['nojual']))->sort();
        $remainingPayment = $amount;

        // Pay invoices sequentially until payment amount is exhausted
        foreach ($invoiceList as $invoice) {
            $invoice = trim($invoice);
            $total = DB::table('tb_jual')->where('nojual', $invoice)->value('total');
            $paid = DB::table('tb_jualbayar')->where('nomor_inv', $invoice)->sum('jmlbayar');
            $balance = $total - $paid;

            if ($remainingPayment <= 0) {
                break;
            }

            $paymentAmount = min($balance, $remainingPayment);
            $remainingPayment -= $paymentAmount;

            if ($paymentAmount > 0) {
                JualBayar::query()->create([
                    'nobayar' => $paymentNumber,
                    'tglbayar' => $_POST['tglbyr'],
                    'nomor_inv' => $invoice,
                    'cust_bayarinv' => $_POST['cust'],
                    'pay_rekbayar' => $_POST['rekbyr'],
                    'jmlbayar' => $paymentAmount,
                    'cashflow' => $_POST['cashflow'],
                ]);
            }
        }

        JualBayar::query()->where('nobayar', $paymentNumber)->first()?->syncJournalEntry();

        DB::commit();

        echo "<script>
            setTimeout(function() {
                swal({
                    title: 'Transaksi Pembayaran',
                    text: 'Berhasil Disimpan!',
                    type: 'success'
                }, function() {
                    window.location = '?page=byrpiutang';
                });
            }, 100);
        </script>";
    } catch (\Exception $e) {
        DB::rollback();
        throw $e;
    }
}
?>

<section class="content">
    <div class="box box-info box-solid">
        <div class="box-header with-border">
            INPUT PEMBAYARAN PIUTANG
        </div>
        <div class="panel-body">
            <div class="col-md-4">
                <div class="table-responsive">
                    <form method="post">
                        <table class="table">
                            <tr>
                                <td>Transaksi</td>
                                <td>:</td>
                                <td>
                                    <input type="text" name="nojual" class="form-control" required value="<?= implode(', ', $invoices) ?>" readonly>
                                </td>
                            </tr>
                            <tr>
                                <td>Tanggal</td>
                                <td>:</td>
                                <td>
                                    <input type="date" name="tglbeli" class="form-control" value="<?= $sale->tgljual ?>" readonly>
                                </td>
                            </tr>
                            <tr>
                                <td>Jatuh Tempo</td>
                                <td>:</td>
                                <td>
                                    <input type="date" name="tgljt" class="form-control" value="<?= $sale->jt ?>" readonly>
                                </td>
                            </tr>
                            <tr>
                                <td>Customer</td>
                                <td>:</td>
                                <td>
                                    <select name="cust" id="cust" class="form-control" required readonly>
                                        <option value="<?= $sale->kdcust ?>"><?= $sale->nama_cust ?></option>
                                        <?php foreach (Customer::orderBy('nama_cust')->get() as $customer) { ?>
                                            <option value="<?= $customer->cust_kd ?>"><?= $customer->nama_cust ?></option>
                                        <?php } ?>
                                    </select>
                                </td>
                            </tr>
                        </table>

                        <hr>

                        <table class="table">
                            <tr class="bg-primary">
                                <td colspan="3">
                                    <font color="#fff">Detail Pembayaran</font>
                                </td>
                            </tr>
                            <tr>
                                <td><b>No. Bayar</b></td>
                                <td>:</td>
                                <td>
                                    <input type="text" name="nobyr" id="nobyr" class="form-control" disabled value="[Otomatis]">
                                </td>
                            </tr>
                            <tr>
                                <td><b>Tanggal</b></td>
                                <td>:</td>
                                <td>
                                    <input type="date" name="tglbyr" class="form-control" value="<?= date('Y-m-d') ?>" required>
                                </td>
                            </tr>
                            <tr>
                                <td><b>Rekening</b></td>
                                <td>:</td>
                                <td>
                                    <select class="form-control" name="rekbyr" id="rekbyr" required>
                                        <option value="">-Pilih-</option>
                                        <?php foreach (Rekening::whereRaw("LEFT(norek, 3) IN ('111', '112')")->orderBy('norek')->get() as $account) { ?>
                                            <option value="<?= $account->norek ?>"><?= $account->nmrek ?></option>
                                        <?php } ?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td><b>Jumlah</b></td>
                                <td>:</td>
                                <td>
                                    <input type="text" name="jmlbyr" id="jmlbyr" class="text-right form-control" style="font-size: 24px; background-color: #f8d6c3;" value="<?= number_format($remaining, 0, ',', '.') ?>" required>
                                </td>
                            </tr>
                            <tr>
                                <td><b>Arus Kas</b></td>
                                <td>:</td>
                                <td>
                                    <select name="cashflow" class="form-control" required>
                                        <option value="">- Pilih -</option>
                                        <?php
                                        foreach (JurnalDetail::CASHFLOWS as $flow) {
                                            echo "<option value=\"{$flow}\">{$flow}</option>";
                                        }
?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" class="text-right">
                                    <hr>
                                    <a href="?page=byrpiutang" class="btn btn-sm btn-danger">Exit</a>
                                    <button type="reset" class="btn btn-sm btn-warning">Clear</button>
                                    <button type="submit" name="tambah" class="btn btn-sm btn-primary">Simpan</button>
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>

            <div class="col-md-8">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover table-striped">
                        <thead>
                            <tr class="bg-primary">
                                <th class="text-center">No</th>
                                <th class="text-center">Nama Barang</th>
                                <th class="text-center">Harga</th>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Diskon</th>
                                <th class="text-center">Jumlah</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $no = 1;
foreach ($details as $detail) { ?>
                                <tr>
                                    <td class="text-center"><?= $no++ ?></td>
                                    <td><?= $detail->nama_barang ?></td>
                                    <td class="text-right"><?= number_format($detail->hrgsat, 2, ',', '.') ?></td>
                                    <td class="text-right"><?= number_format($detail->qty) ?></td>
                                    <td class="text-right"><?= number_format($detail->jmldisc) ?></td>
                                    <td class="text-right"><?= number_format($detail->sharga, 2, ',', '.') ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="5" class="text-right">JUMLAH</td>
                                <td class="text-right"><?= number_format($totals->subtotal, 2, ',', '.') ?></td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-right">ONGKIR</td>
                                <td class="text-right"><?= number_format($totals->shipping, 2, ',', '.') ?></td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-right">PACKING</td>
                                <td class="text-right"><?= number_format($totals->packing, 2, ',', '.') ?></td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-right">DIBAYAR</td>
                                <td class="text-right"><?= number_format($paid, 2, ',', '.') ?></td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-right">SISA PIUTANG</td>
                                <td class="text-right"><?= number_format($remaining, 2, ',', '.') ?></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(document).ready(function() {
    // Set initial focus
    document.getElementById('rekbyr').focus();

    // Format currency input
    $('#jmlbyr').on('keyup', function(e) {
        let value = this.value.replace(/[^\d]/g, '');
        value = new Intl.NumberFormat('id-ID').format(value);
        this.value = value;
    });
});
</script>
